<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
      <RootNamespace>HighCapital.Infrastructure</RootNamespace>
      <AssemblyName>HighCapital.Infrastructure</AssemblyName>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Azure.Identity" />
      <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" />
      <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Application\Application.csproj" />
    </ItemGroup>
  </Project>