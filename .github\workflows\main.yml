name: Publish NuGet

on:
  push:
    branches:
     - main
    tags:
      - "v*"

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: "9.0.x"
      - name: Build
        run: dotnet build --configuration Release
      - name: Pack
        run: dotnet pack --configuration Release -o out
      - name: Publish
        run: dotnet nuget push "out/*.nupkg" --api-key ${{ secrets.NUGET_TOKEN }} --source "https://nuget.pkg.github.com/HighCapitalTech/index.json"
